import subprocess
import time
import ctypes
import sys
import pyauto<PERSON><PERSON>
 
def run_as_admin():
    """
    以管理模式运行Python文件
    """
    try:
        is_admin = bool(ctypes.windll.shell32.IsUserAnAdmin())
    except:
        is_admin = False
 
    if not is_admin:
        ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, __file__, None, 1)
        exit()
 
 
def get_sessions():
    """
    获取当前Windows操作系统的全部会话信息
    :return: [[session_name, session_id, session_status], ...], 如下所示：
            [
                ['services', '0', '断开'],
                ['>console', '1', '运行中'],
                ['rdp-tcp', '65536', '侦听']
            ]
    """
 
    # 创建子进程，执行“query session”命令，并捕获输出的内容
    process = subprocess.run(['query', 'session'], capture_output=True)
 
    # 获取标准输出原始数据，解码，并以每行数据转换成列表
    # 原始数据如下：
    #  会话名            用户名                   ID  状态    类型        设备
    #  services                                    0  断开
    # >console           Frank                     1  运行中
    #  rdp-tcp                                 65536  侦听
    # 转换后的数据:
    # [
    #     ['会话名', '用户名', 'ID', '状态', '类型'],
    #     ['services', '0', '断开'],
    #     ['>console', 'Frank', '1', '运行中'],
    #     ['rdp-tcp', '65536', '侦听'],
    #     []
    # ]
    original_data = process.stdout.decode('ansi').split('\r\n')
    sessions = []
    for x in original_data:
        temp = []
        x_list = x.split(' ')
        empty_quantity = x_list.count('')
        for number in range(empty_quantity):
            if x_list[number] != '':
                temp.append(x_list[number])
        sessions.append(temp)
 
    # 去头 => ['会话名', '用户名', 'ID', '状态', '类型']，去尾 => []
    sessions.pop(0)
    sessions.pop(-1)
 
    # 再加工，仅仅保留 [session_name, session_id, session_status]
    for session in sessions:
        if len(session) == 4:
            session.pop(1)
 
    # 返回经过加工处理的会话列表数据
    return sessions
 
 
def get_current_session():
    """
    获取当前正在运行中的session信息
    :return: [session_name, session_id, session_status]
    """
    # 获取全部会话信息
    sessions = get_sessions()

    # 遍历获取会话名中带'>'的会话，
    for session in sessions:
        if session[0][0] == '>':
            return session


def send_hotkey_combination(keys):
    """
    发送组合快捷键
    :param keys: 快捷键组合，例如 ['ctrl', 'alt', 'r'] 或 ['win', 'l']
    """
    try:
        pyautogui.hotkey(*keys)
        print(f"已发送快捷键组合: {'+'.join(keys)}")
    except Exception as e:
        print(f"发送快捷键失败: {e}")


def is_remote_session(session_name):
    """
    判断是否为远程桌面会话
    :param session_name: 会话名称
    :return: True表示远程会话，False表示本地会话
    """
    # 远程桌面会话名格式通常为 ">rdp-tcp#数字" 或包含"rdp"
    return session_name.startswith('>') and len(session_name) > 1 and 'rdp' in session_name.lower()
 
 
if __name__ == '__main__':

    # 以管理员模式运行当前程序
    run_as_admin()

    # 配置快捷键组合
    # 远程桌面连接时的快捷键（可自定义修改）
    REMOTE_CONNECT_HOTKEY = ['ctrl', 'alt', 'r']
    # 远程桌面断开时的快捷键（可自定义修改）
    REMOTE_DISCONNECT_HOTKEY = ['ctrl', 'alt', 'd']

    # 初始化状态跟踪变量
    previous_is_remote = None

    print("远程桌面监控程序已启动...")
    print(f"远程连接快捷键: {'+'.join(REMOTE_CONNECT_HOTKEY)}")
    print(f"远程断开快捷键: {'+'.join(REMOTE_DISCONNECT_HOTKEY)}")
    print("=" * 50)

    # 监测远程会话是否已退出，若退出则将会话切换成本地会话。
    # 远程桌面未关闭前，session_name显示如“>rdp-tcp#2”，
    # 远程桌面退出后，session_name显示成'>'，
    while True:
        try:
            # 获取当前session信息
            current_session = get_current_session()
            if current_session is None:
                print("未找到当前活动会话，等待下次检测...")
                time.sleep(3)
                continue

            session_name, session_id, session_status = current_session
            current_is_remote = is_remote_session(session_name)

            # 检测状态变化并触发相应快捷键
            if previous_is_remote is not None and previous_is_remote != current_is_remote:
                if current_is_remote:
                    # 从本地切换到远程
                    print(f"检测到远程桌面连接: {session_name}")
                    send_hotkey_combination(REMOTE_CONNECT_HOTKEY)
                else:
                    # 从远程切换到本地
                    print(f"检测到远程桌面断开: {session_name}")
                    send_hotkey_combination(REMOTE_DISCONNECT_HOTKEY)

            # 判断远程桌面是否已断开，断开就切换成本地会话
            if session_name == '>':
                print(f"执行会话切换: tscon {session_id} /dest:console")
                subprocess.run(['tscon', session_id, '/dest:console'])

            # 更新状态
            previous_is_remote = current_is_remote

        except Exception as e:
            print(f"监控过程中发生错误: {e}")

        # 每隔3秒监测1次
        time.sleep(3)